import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:venta_cuba/Controllers/auth_controller.dart';
import '../Controller/ChatController.dart';
import '../custom_text.dart';

class FirebaseDebugScreen extends StatefulWidget {
  @override
  _FirebaseDebugScreenState createState() => _FirebaseDebugScreenState();
}

class _FirebaseDebugScreenState extends State<FirebaseDebugScreen> {
  final authCont = Get.find<AuthController>();
  bool isTestingConnection = false;
  Map<String, dynamic>? lastTestResults;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('🔥 Firebase Debug'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Card(
              color: Colors.orange.shade50,
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Firebase Connection Tester',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade800,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'This screen helps test if Firebase services are working properly in Cuba. Please screenshot any error messages and send to developers.',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 20),

            // Test Connection Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: isTestingConnection ? null : _testConnection,
                icon: isTestingConnection
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Icon(Icons.wifi_find),
                label: Text(isTestingConnection
                    ? 'Testing...'
                    : 'Test Firebase Connection'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            SizedBox(height: 20),

            // Current Status
            _buildStatusCard(),

            SizedBox(height: 20),

            // Test Results
            if (lastTestResults != null) _buildTestResults(),

            SizedBox(height: 20),

            // Device Info
            _buildDeviceInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Status',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            _buildStatusRow('User Logged In', authCont.user != null),
            _buildStatusRow('Device Token Available', deviceToken.isNotEmpty),
            _buildStatusRow(
                'Chat Controller Ready', Get.isRegistered<ChatController>()),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool status) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            status ? Icons.check_circle : Icons.error,
            color: status ? Colors.green : Colors.red,
            size: 20,
          ),
          SizedBox(width: 8),
          Expanded(child: Text(label)),
          Text(
            status ? 'OK' : 'FAIL',
            style: TextStyle(
              color: status ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestResults() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Results',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            ...lastTestResults!.entries.map((entry) {
              Map<String, dynamic> result = entry.value;
              bool success = result['success'] ?? false;
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          success ? Icons.check_circle : Icons.error,
                          color: success ? Colors.green : Colors.red,
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Expanded(child: Text(entry.key)),
                        Text(
                          success ? 'PASS' : 'FAIL',
                          style: TextStyle(
                            color: success ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    if (!success && result['error'] != null)
                      Padding(
                        padding: EdgeInsets.only(left: 28, top: 4),
                        child: SelectableText(
                          'Error: ${result['error']}',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 12.sp,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceInfo() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Device Information',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            _buildInfoRow('User ID',
                authCont.user?.userId?.toString() ?? 'Not logged in'),
            _buildInfoRow(
                'Device Token',
                deviceToken.isEmpty
                    ? 'Not available'
                    : '${deviceToken.substring(0, 20)}...'),
            _buildInfoRow(
                'User Email', authCont.user?.email ?? 'Not available'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: SelectableText(
              value,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _testConnection() async {
    setState(() {
      isTestingConnection = true;
      lastTestResults = null;
    });

    try {
      Map<String, dynamic> results =
          await ChatDebugHelper.testFirebaseConnection();
      setState(() {
        lastTestResults = results;
        isTestingConnection = false;
      });
    } catch (e) {
      setState(() {
        isTestingConnection = false;
      });

      Get.dialog(
        AlertDialog(
          title: Text('Test Failed'),
          content: Text('Failed to run connection test: $e'),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('OK'),
            ),
          ],
        ),
      );
    }
  }
}
