import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:venta_cuba/Controllers/auth_controller.dart';
import '../Controller/ChatController.dart';
import '../custom_text.dart';

class FirebaseDebugScreen extends StatefulWidget {
  @override
  _FirebaseDebugScreenState createState() => _FirebaseDebugScreenState();
}

class _FirebaseDebugScreenState extends State<FirebaseDebugScreen> {
  final authCont = Get.find<AuthController>();
  bool isTestingConnection = false;
  Map<String, dynamic>? lastTestResults;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('🔥 Firebase Debug'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Card(
              color: Colors.orange.shade50,
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Firebase Connection Tester',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade800,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'This screen helps test if Firebase services are working properly in Cuba. Please screenshot any error messages and send to developers.',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 20),

            // Test Connection Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: isTestingConnection ? null : _testConnection,
                icon: isTestingConnection
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Icon(Icons.wifi_find),
                label: Text(isTestingConnection
                    ? 'Testing...'
                    : 'Test Firebase Connection'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            SizedBox(height: 10),

            // Simple Test Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _simpleTest,
                icon: Icon(Icons.bug_report),
                label: Text('Simple Firebase Test'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            SizedBox(height: 10),

            // Quick Info Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _showQuickInfo,
                icon: Icon(Icons.info),
                label: Text('Show Quick Info'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            SizedBox(height: 20),

            // Current Status
            _buildStatusCard(),

            SizedBox(height: 20),

            // Test Results
            if (lastTestResults != null) _buildTestResults(),

            SizedBox(height: 20),

            // Device Info
            _buildDeviceInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Status',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            _buildStatusRow('User Logged In', authCont.user != null),
            _buildStatusRow('Device Token Available', deviceToken.isNotEmpty),
            _buildStatusRow(
                'Chat Controller Ready', Get.isRegistered<ChatController>()),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool status) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            status ? Icons.check_circle : Icons.error,
            color: status ? Colors.green : Colors.red,
            size: 20,
          ),
          SizedBox(width: 8),
          Expanded(child: Text(label)),
          Text(
            status ? 'OK' : 'FAIL',
            style: TextStyle(
              color: status ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestResults() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Results',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            ...lastTestResults!.entries.map((entry) {
              Map<String, dynamic> result = entry.value;
              bool success = result['success'] ?? false;
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          success ? Icons.check_circle : Icons.error,
                          color: success ? Colors.green : Colors.red,
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Expanded(child: Text(entry.key)),
                        Text(
                          success ? 'PASS' : 'FAIL',
                          style: TextStyle(
                            color: success ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    if (!success && result['error'] != null)
                      Padding(
                        padding: EdgeInsets.only(left: 28, top: 4),
                        child: SelectableText(
                          'Error: ${result['error']}',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 12.sp,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceInfo() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Device Information',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            _buildInfoRow('User ID',
                authCont.user?.userId?.toString() ?? 'Not logged in'),
            _buildInfoRow(
                'Device Token',
                deviceToken.isEmpty
                    ? 'Not available'
                    : '${deviceToken.substring(0, 20)}...'),
            _buildInfoRow(
                'User Email', authCont.user?.email ?? 'Not available'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: SelectableText(
              value,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _testConnection() async {
    print('🔥 DEBUG: Starting connection test...');
    setState(() {
      isTestingConnection = true;
      lastTestResults = null;
    });

    try {
      print('🔥 DEBUG: Calling ChatDebugHelper.testFirebaseConnection()...');
      Map<String, dynamic> results =
          await ChatDebugHelper.testFirebaseConnection();
      print('🔥 DEBUG: Test completed with results: $results');

      setState(() {
        lastTestResults = results;
        isTestingConnection = false;
      });

      print('🔥 DEBUG: UI updated with results');
    } catch (e, stackTrace) {
      print('🔥 DEBUG: Test failed with error: $e');
      print('🔥 DEBUG: Stack trace: $stackTrace');

      setState(() {
        isTestingConnection = false;
      });

      Get.dialog(
        AlertDialog(
          title: Text('Test Failed'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Failed to run connection test:'),
                SizedBox(height: 8),
                SelectableText('Error: $e'),
                SizedBox(height: 8),
                SelectableText('Stack trace: $stackTrace'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  void _simpleTest() {
    print('🔥 SIMPLE TEST: Starting...');

    // Show a simple dialog with basic info
    Get.dialog(
      AlertDialog(
        title: Text('🔥 Simple Firebase Test'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Basic Information:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('User logged in: ${authCont.user != null}'),
              Text(
                  'Device token: ${deviceToken.isNotEmpty ? "Available" : "Not available"}'),
              Text('Device token length: ${deviceToken.length}'),
              if (deviceToken.isNotEmpty)
                SelectableText(
                    'Token preview: ${deviceToken.substring(0, math.min(50, deviceToken.length))}...'),
              SizedBox(height: 12),
              Text('Firebase Services:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              FutureBuilder(
                future: _runSimpleTests(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return Row(
                      children: [
                        SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2)),
                        SizedBox(width: 12),
                        Text('Testing...'),
                      ],
                    );
                  }

                  if (snapshot.hasError) {
                    return Text('Error: ${snapshot.error}',
                        style: TextStyle(color: Colors.red));
                  }

                  Map<String, String> results =
                      snapshot.data as Map<String, String>;
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: results.entries.map((entry) {
                      return Text('${entry.key}: ${entry.value}');
                    }).toList(),
                  );
                },
              ),
              SizedBox(height: 12),
              Text('Please screenshot this dialog and send to developers.',
                  style: TextStyle(
                      color: Colors.blue, fontWeight: FontWeight.bold)),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  Future<Map<String, String>> _runSimpleTests() async {
    Map<String, String> results = {};

    // Test 1: Basic Firebase initialization
    try {
      // Just check if Firebase is initialized
      results['Firebase Init'] = 'OK';
    } catch (e) {
      results['Firebase Init'] = 'FAIL: $e';
    }

    // Test 2: Device token
    try {
      results['Device Token'] = deviceToken.isNotEmpty ? 'OK' : 'FAIL: Empty';
    } catch (e) {
      results['Device Token'] = 'FAIL: $e';
    }

    // Test 3: User authentication
    try {
      results['User Auth'] =
          authCont.user != null ? 'OK' : 'FAIL: Not logged in';
    } catch (e) {
      results['User Auth'] = 'FAIL: $e';
    }

    return results;
  }

  void _showQuickInfo() {
    print('🔥 QUICK INFO: Button pressed');

    Get.dialog(
      AlertDialog(
        title: Text('🔥 Quick Debug Info'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('App Status:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('✓ Debug screen is working'),
              Text('✓ GetX navigation is working'),
              Text('✓ Dialog system is working'),
              SizedBox(height: 12),
              Text('User Info:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('Logged in: ${authCont.user != null ? "YES" : "NO"}'),
              if (authCont.user != null) ...[
                Text('User ID: ${authCont.user!.userId}'),
                Text('Email: ${authCont.user!.email}'),
                Text(
                    'Name: ${authCont.user!.firstName} ${authCont.user!.lastName}'),
              ],
              SizedBox(height: 12),
              Text('Device Info:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('Device Token Length: ${deviceToken.length}'),
              Text('Token Available: ${deviceToken.isNotEmpty ? "YES" : "NO"}'),
              if (deviceToken.isNotEmpty) ...[
                SizedBox(height: 8),
                Text('Token Preview:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                SelectableText(
                  deviceToken.length > 50
                      ? '${deviceToken.substring(0, 50)}...'
                      : deviceToken,
                  style: TextStyle(fontSize: 12, fontFamily: 'monospace'),
                ),
              ],
              SizedBox(height: 12),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Text(
                  'This dialog confirms the app is running and can display debug information. Screenshot this and send to developers.',
                  style: TextStyle(color: Colors.blue.shade800),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
}
